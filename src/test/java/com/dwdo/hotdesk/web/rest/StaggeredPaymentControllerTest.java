package com.dwdo.hotdesk.web.rest;

import com.dwdo.hotdesk.dto.PaymentDataDTO;
import com.dwdo.hotdesk.dto.SubmissionHistoryDTO;
import com.dwdo.hotdesk.dto.SubmissionDetailDTO;
import com.dwdo.hotdesk.dto.TaskListDTO;
import com.dwdo.hotdesk.dto.request.SingleValidRequestDTO;
import com.dwdo.hotdesk.dto.response.GeneralBodyResponse;
import com.dwdo.hotdesk.restcontrolleradvice.CustomBadRequestException;
import com.dwdo.hotdesk.service.FileTemplateService;
import com.dwdo.hotdesk.service.PaymentValidationService;
import com.dwdo.hotdesk.service.ReportService;
import com.dwdo.hotdesk.service.SubmissionHistoryService;
import com.dwdo.hotdesk.service.SubmissionDetailService;
import com.dwdo.hotdesk.service.TaskListService;
import com.dwdo.hotdesk.service.feign.response.ApiResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.hamcrest.Matchers.containsString;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Comprehensive unit tests for StaggeredPaymentController
 * Tests all endpoints: bulk-validate, single-validate, template-submission, template-payment
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("Staggered Payment Controller Tests")
class StaggeredPaymentControllerTest {

    @Mock
    private PaymentValidationService validationService;

    @Mock
    private FileTemplateService fileTemplateService;

    @Mock
    private SubmissionHistoryService submissionHistoryService;

    @Mock
    private SubmissionDetailService submissionDetailService;

    @Mock
    private TaskListService taskListService;

    @Mock
    private ReportService reportService;

    @InjectMocks
    private StaggeredPaymentController staggeredPaymentController;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(staggeredPaymentController).build();
        objectMapper = new ObjectMapper();
    }

    @Nested
    @DisplayName("Bulk Validate Endpoint Tests")
    class BulkValidateTests {

        @Test
        @DisplayName("Should successfully validate Excel file with single record")
        void testBulkValidate_Success_SingleRecord() throws Exception {
            // Given
            MockMultipartFile file = new MockMultipartFile(
                    "file",
                    "test-payments.xlsx",
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    "test content".getBytes()
            );

            List<PaymentDataDTO> mockPaymentData = Arrays.asList(
                    PaymentDataDTO.builder()
                            .nip("123456789")
                            .name("John Doe")
                            .grade("A")
                            .paymentType("Salary")
                            .amount(new BigDecimal("5000000"))
                            .description("Monthly salary")
                            .monthOfProcess("January")
                            .yearOfProcess("2024")
                            .nipValid("NIP is valid")
                            .directorate("IT")
                            .eligible(true)
                            .slik("-")
                            .sanction("-")
                            .terminationDate("-")
                            .build()
            );

            GeneralBodyResponse expectedResponse = GeneralBodyResponse.builder()
                    .code(200)
                    .status("OK")
                    .message("File validated successfully")
                    .data(mockPaymentData)
                    .build();

            when(validationService.validateExcelFile(any(MultipartFile.class)))
                    .thenReturn(expectedResponse);

            // When & Then
            mockMvc.perform(multipart("/api/staggered/bulk-validate")
                            .file(file)
                            .contentType(MediaType.MULTIPART_FORM_DATA))
                    .andExpect(status().isOk())
                    .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                    .andExpect(jsonPath("$.code").value(200))
                    .andExpect(jsonPath("$.status").value("OK"))
                    .andExpect(jsonPath("$.message").value("File validated successfully"))
                    .andExpect(jsonPath("$.data").isArray())
                    .andExpect(jsonPath("$.data[0].nip").value("123456789"))
                    .andExpect(jsonPath("$.data[0].name").value("John Doe"))
                    .andExpect(jsonPath("$.data[0].eligible").value(true))
                    .andExpect(jsonPath("$.data[0].directorate").value("IT"));

            verify(validationService, times(1)).validateExcelFile(any(MultipartFile.class));
        }

        @Test
        @DisplayName("Should successfully validate Excel file with multiple records")
        void testBulkValidate_Success_MultipleRecords() throws Exception {
            // Given
            MockMultipartFile file = new MockMultipartFile(
                    "file",
                    "multiple-payments.xlsx",
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    "test content with multiple records".getBytes()
            );

            List<PaymentDataDTO> mockPaymentData = Arrays.asList(
                    PaymentDataDTO.builder()
                            .nip("123456789")
                            .name("John Doe")
                            .eligible(true)
                            .directorate("IT")
                            .build(),
                    PaymentDataDTO.builder()
                            .nip("987654321")
                            .name("Jane Smith")
                            .eligible(false)
                            .directorate("HR")
                            .build()
            );

            GeneralBodyResponse expectedResponse = GeneralBodyResponse.builder()
                    .code(200)
                    .status("OK")
                    .message("File validated successfully")
                    .data(mockPaymentData)
                    .build();

            when(validationService.validateExcelFile(any(MultipartFile.class)))
                    .thenReturn(expectedResponse);

            // When & Then
            mockMvc.perform(multipart("/api/staggered/bulk-validate")
                            .file(file)
                            .contentType(MediaType.MULTIPART_FORM_DATA))
                    .andExpect(status().isOk())
                    .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                    .andExpect(jsonPath("$.code").value(200))
                    .andExpect(jsonPath("$.status").value("OK"))
                    .andExpect(jsonPath("$.data").isArray())
                    .andExpect(jsonPath("$.data.length()").value(2))
                    .andExpect(jsonPath("$.data[0].nip").value("123456789"))
                    .andExpect(jsonPath("$.data[1].nip").value("987654321"));

            verify(validationService, times(1)).validateExcelFile(any(MultipartFile.class));
        }

        @Test
        @DisplayName("Should handle invalid file type (non-Excel)")
        void testBulkValidate_InvalidFileType() throws Exception {
            // Given
            MockMultipartFile file = new MockMultipartFile(
                    "file",
                    "test.txt",
                    "text/plain",
                    "invalid content".getBytes()
            );

            when(validationService.validateExcelFile(any(MultipartFile.class)))
                    .thenThrow(new CustomBadRequestException(400, "Invalid File", "Only Excel files are allowed"));

            // When & Then
            mockMvc.perform(multipart("/api/staggered/bulk-validate")
                            .file(file)
                            .contentType(MediaType.MULTIPART_FORM_DATA))
                    .andExpect(status().isBadRequest());

            verify(validationService, times(1)).validateExcelFile(any(MultipartFile.class));
        }

        @Test
        @DisplayName("Should handle corrupted Excel file")
        void testBulkValidate_CorruptedFile() throws Exception {
            // Given
            MockMultipartFile corruptedFile = new MockMultipartFile(
                    "file",
                    "corrupted.xlsx",
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    "corrupted content".getBytes()
            );

            when(validationService.validateExcelFile(any(MultipartFile.class)))
                    .thenThrow(new CustomBadRequestException(400, "Processing Error", "Failed to process the Excel file"));

            // When & Then
            mockMvc.perform(multipart("/api/staggered/bulk-validate")
                            .file(corruptedFile)
                            .contentType(MediaType.MULTIPART_FORM_DATA))
                    .andExpect(status().isBadRequest());

            verify(validationService, times(1)).validateExcelFile(any(MultipartFile.class));
        }

        @Test
        @DisplayName("Should handle empty Excel file")
        void testBulkValidate_EmptyFile() throws Exception {
            // Given
            MockMultipartFile emptyFile = new MockMultipartFile(
                    "file",
                    "empty.xlsx",
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    new byte[0]
            );

            when(validationService.validateExcelFile(any(MultipartFile.class)))
                    .thenThrow(new CustomBadRequestException(400, "Processing Error", "File is empty"));

            // When & Then
            mockMvc.perform(multipart("/api/staggered/bulk-validate")
                            .file(emptyFile)
                            .contentType(MediaType.MULTIPART_FORM_DATA))
                    .andExpect(status().isBadRequest());

            verify(validationService, times(1)).validateExcelFile(any(MultipartFile.class));
        }

        @Test
        @DisplayName("Should handle service exception during validation")
        void testBulkValidate_ServiceException() throws Exception {
            // Given
            MockMultipartFile file = new MockMultipartFile(
                    "file",
                    "test.xlsx",
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    "test content".getBytes()
            );

            when(validationService.validateExcelFile(any(MultipartFile.class)))
                    .thenThrow(new RuntimeException("Unexpected service error"));

            // When & Then
            mockMvc.perform(multipart("/api/staggered/bulk-validate")
                            .file(file)
                            .contentType(MediaType.MULTIPART_FORM_DATA))
                    .andExpect(status().isInternalServerError());

            verify(validationService, times(1)).validateExcelFile(any(MultipartFile.class));
        }

        @Test
        @DisplayName("Should handle missing file parameter")
        void testBulkValidate_MissingFile() throws Exception {
            // When & Then - No file parameter provided
            mockMvc.perform(multipart("/api/staggered/bulk-validate")
                            .contentType(MediaType.MULTIPART_FORM_DATA))
                    .andExpect(status().isBadRequest());

            verify(validationService, never()).validateExcelFile(any(MultipartFile.class));
        }

        @Test
        @DisplayName("Should verify service method is called exactly once")
        void testBulkValidate_ServiceCallVerification() throws Exception {
            // Given
            MockMultipartFile file = new MockMultipartFile(
                    "file",
                    "test.xlsx",
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    "test content".getBytes()
            );

            List<PaymentDataDTO> mockPaymentData = Arrays.asList(
                    PaymentDataDTO.builder()
                            .nip("123456789")
                            .name("Test User")
                            .eligible(true)
                            .build()
            );

            GeneralBodyResponse expectedResponse = GeneralBodyResponse.builder()
                    .code(200)
                    .status("OK")
                    .message("File validated successfully")
                    .data(mockPaymentData)
                    .build();

            when(validationService.validateExcelFile(any(MultipartFile.class)))
                    .thenReturn(expectedResponse);

            // When
            mockMvc.perform(multipart("/api/staggered/bulk-validate")
                            .file(file)
                            .contentType(MediaType.MULTIPART_FORM_DATA))
                    .andExpect(status().isOk());

            // Then - Verify service is called exactly once
            verify(validationService, times(1)).validateExcelFile(any(MultipartFile.class));
            verifyNoMoreInteractions(validationService);
        }
    }

    @Nested
    @DisplayName("Single Validate Endpoint Tests")
    class SingleValidateTests {

        @Test
        @DisplayName("Should successfully validate single payment with valid data")
        void testSingleValidate_Success() throws Exception {
            // Given
            SingleValidRequestDTO request = SingleValidRequestDTO.builder()
                    .nip("123456789")
                    .name("John Doe")
                    .grade("A")
                    .paymentType("Salary")
                    .amount(new BigDecimal("5000000"))
                    .description("Monthly salary")
                    .monthOfProcess("January")
                    .yearOfProcess("2024")
                    .build();

            PaymentDataDTO mockPaymentData = PaymentDataDTO.builder()
                    .nip("123456789")
                    .name("John Doe")
                    .grade("A")
                    .paymentType("Salary")
                    .amount(new BigDecimal("5000000"))
                    .description("Monthly salary")
                    .monthOfProcess("January")
                    .yearOfProcess("2024")
                    .nipValid("NIP is valid")
                    .directorate("IT")
                    .eligible(true)
                    .slik("-")
                    .sanction("-")
                    .terminationDate("-")
                    .build();

            GeneralBodyResponse expectedResponse = GeneralBodyResponse.builder()
                    .code(200)
                    .status("OK")
                    .message("Payment data validated successfully")
                    .data(mockPaymentData)
                    .build();

            when(validationService.validateSinglePayment(any(SingleValidRequestDTO.class)))
                    .thenReturn(expectedResponse);

            // When & Then
            mockMvc.perform(post("/api/staggered/single-validate")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(request)))
                    .andExpect(status().isOk())
                    .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                    .andExpect(jsonPath("$.code").value(200))
                    .andExpect(jsonPath("$.status").value("OK"))
                    .andExpect(jsonPath("$.message").value("Payment data validated successfully"))
                    .andExpect(jsonPath("$.data.nip").value("123456789"))
                    .andExpect(jsonPath("$.data.name").value("John Doe"))
                    .andExpect(jsonPath("$.data.eligible").value(true))
                    .andExpect(jsonPath("$.data.directorate").value("IT"))
                    .andExpect(jsonPath("$.data.nipValid").value("NIP is valid"));

            verify(validationService, times(1)).validateSinglePayment(any(SingleValidRequestDTO.class));
        }

        @Test
        @DisplayName("Should handle single payment validation with invalid employee data")
        void testSingleValidate_InvalidEmployee() throws Exception {
            // Given
            SingleValidRequestDTO request = SingleValidRequestDTO.builder()
                    .nip("999999999")
                    .name("Invalid User")
                    .grade("B")
                    .paymentType("Bonus")
                    .amount(new BigDecimal("2000000"))
                    .description("Performance bonus")
                    .monthOfProcess("February")
                    .yearOfProcess("2024")
                    .build();

            PaymentDataDTO mockPaymentData = PaymentDataDTO.builder()
                    .nip("999999999")
                    .name("Invalid User")
                    .grade("B")
                    .paymentType("Bonus")
                    .amount(new BigDecimal("2000000"))
                    .description("Performance bonus")
                    .monthOfProcess("February")
                    .yearOfProcess("2024")
                    .nipValid("NIP is invalid")
                    .directorate("-")
                    .eligible(false)
                    .slik("Has SLIK issues")
                    .sanction("Has sanctions")
                    .terminationDate("-")
                    .build();

            GeneralBodyResponse expectedResponse = GeneralBodyResponse.builder()
                    .code(200)
                    .status("OK")
                    .message("Payment data validated successfully")
                    .data(mockPaymentData)
                    .build();

            when(validationService.validateSinglePayment(any(SingleValidRequestDTO.class)))
                    .thenReturn(expectedResponse);

            // When & Then
            mockMvc.perform(post("/api/staggered/single-validate")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(request)))
                    .andExpect(status().isOk())
                    .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                    .andExpect(jsonPath("$.code").value(200))
                    .andExpect(jsonPath("$.status").value("OK"))
                    .andExpect(jsonPath("$.data.nip").value("999999999"))
                    .andExpect(jsonPath("$.data.eligible").value(false))
                    .andExpect(jsonPath("$.data.nipValid").value("NIP is invalid"))
                    .andExpect(jsonPath("$.data.directorate").value("-"))
                    .andExpect(jsonPath("$.data.slik").value("Has SLIK issues"))
                    .andExpect(jsonPath("$.data.sanction").value("Has sanctions"));

            verify(validationService, times(1)).validateSinglePayment(any(SingleValidRequestDTO.class));
        }

        @Test
        @DisplayName("Should handle validation error for invalid NIP")
        void testSingleValidate_InvalidNip() throws Exception {
            // Given
            SingleValidRequestDTO request = SingleValidRequestDTO.builder()
                    .nip("invalid-nip")
                    .name("Invalid User")
                    .grade("C")
                    .paymentType("Salary")
                    .amount(new BigDecimal("3000000"))
                    .description("Monthly salary")
                    .monthOfProcess("March")
                    .yearOfProcess("2024")
                    .build();

            when(validationService.validateSinglePayment(any(SingleValidRequestDTO.class)))
                    .thenThrow(new CustomBadRequestException(400, "Validation Error", "NIP format is invalid"));

            // When & Then
            mockMvc.perform(post("/api/staggered/single-validate")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(request)))
                    .andExpect(status().isBadRequest());

            verify(validationService, times(1)).validateSinglePayment(any(SingleValidRequestDTO.class));
        }

        @Test
        @DisplayName("Should handle missing required fields (empty NIP)")
        void testSingleValidate_MissingRequiredFields() throws Exception {
            // Given - Request with missing required fields (empty NIP)
            SingleValidRequestDTO request = SingleValidRequestDTO.builder()
                    .nip("") // Empty NIP should trigger validation error
                    .name("Test User")
                    .grade("A")
                    .paymentType("Salary")
                    .amount(new BigDecimal("1000000"))
                    .description("Test payment")
                    .monthOfProcess("April")
                    .yearOfProcess("2024")
                    .build();

            // When & Then - Should return 400 due to @Valid annotation
            mockMvc.perform(post("/api/staggered/single-validate")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(request)))
                    .andExpect(status().isBadRequest());

            verify(validationService, never()).validateSinglePayment(any(SingleValidRequestDTO.class));
        }

        @Test
        @DisplayName("Should handle null amount validation")
        void testSingleValidate_NullAmount() throws Exception {
            // Given - Request with null amount
            SingleValidRequestDTO request = SingleValidRequestDTO.builder()
                    .nip("123456789")
                    .name("Test User")
                    .grade("A")
                    .paymentType("Salary")
                    .amount(null) // Null amount should trigger validation error
                    .description("Test payment")
                    .monthOfProcess("May")
                    .yearOfProcess("2024")
                    .build();

            // When & Then - Should return 400 due to @Valid annotation
            mockMvc.perform(post("/api/staggered/single-validate")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(request)))
                    .andExpect(status().isBadRequest());

            verify(validationService, never()).validateSinglePayment(any(SingleValidRequestDTO.class));
        }
    }

    @Nested
    @DisplayName("Template Submission Endpoint Tests")
    class TemplateSubmissionTests {

        @Test
        @DisplayName("Should successfully generate and download staggered payment template")
        void testGetTemplateFile_Success() throws Exception {
            // Given
            byte[] templateContent = createMockExcelContent("Staggered Payment Template");
            ByteArrayResource resource = new ByteArrayResource(templateContent);

            ResponseEntity<Resource> expectedResponse = ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=staggered_payment_template.xlsx")
                    .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
                    .contentLength(resource.contentLength())
                    .body(resource);

            when(fileTemplateService.generateStaggeredPaymentTemplate())
                    .thenReturn(expectedResponse);

            // When & Then
            mockMvc.perform(get("/api/staggered/template-submission"))
                    .andExpect(status().isOk())
                    .andExpect(content().contentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
                    .andExpect(header().exists(HttpHeaders.CONTENT_DISPOSITION))
                    .andExpect(header().string(HttpHeaders.CONTENT_DISPOSITION,
                        containsString("attachment")))
                    .andExpect(header().string(HttpHeaders.CONTENT_DISPOSITION,
                        containsString("submission_template.xlsx")));

            verify(fileTemplateService, times(1)).generateStaggeredPaymentTemplate();
        }

        @Test
        @DisplayName("Should handle service exception during template generation")
        void testGetTemplateFile_ServiceException() throws Exception {
            // Given
            when(fileTemplateService.generateStaggeredPaymentTemplate())
                    .thenThrow(new RuntimeException("Template generation failed"));

            // When & Then
            mockMvc.perform(get("/api/staggered/template-submission"))
                    .andExpect(status().isInternalServerError());

            verify(fileTemplateService, times(1)).generateStaggeredPaymentTemplate();
        }

        @Test
        @DisplayName("Should handle template service returning null")
        void testGetTemplateFile_NullResponse() throws Exception {
            // Given
            when(fileTemplateService.generateStaggeredPaymentTemplate())
                    .thenReturn(null);

            // When & Then
            mockMvc.perform(get("/api/staggered/template-submission"))
                    .andExpect(status().isInternalServerError());

            verify(fileTemplateService, times(1)).generateStaggeredPaymentTemplate();
        }

        @Test
        @DisplayName("Should handle template with different content types")
        void testGetTemplateFile_DifferentContentType() throws Exception {
            // Given
            byte[] templateContent = createMockExcelContent("Alternative Template");
            ByteArrayResource resource = new ByteArrayResource(templateContent);

            ResponseEntity<Resource> expectedResponse = ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=template.xls")
                    .contentType(MediaType.parseMediaType("application/vnd.ms-excel"))
                    .body(resource);

            when(fileTemplateService.generateStaggeredPaymentTemplate())
                    .thenReturn(expectedResponse);

            // When & Then
            mockMvc.perform(get("/api/staggered/template-submission"))
                    .andExpect(status().isOk())
                    .andExpect(content().contentType("application/vnd.ms-excel"))
                    .andExpect(header().string(HttpHeaders.CONTENT_DISPOSITION,
                        containsString("template.xls")));

            verify(fileTemplateService, times(1)).generateStaggeredPaymentTemplate();
        }

        @Test
        @DisplayName("Should handle large template files")
        void testGetTemplateFile_LargeFile() throws Exception {
            // Given - Create a larger mock template
            byte[] largeTemplateContent = new byte[1024 * 1024]; // 1MB
            Arrays.fill(largeTemplateContent, (byte) 'A');
            ByteArrayResource resource = new ByteArrayResource(largeTemplateContent);

            ResponseEntity<Resource> expectedResponse = ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=large_template.xlsx")
                    .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
                    .contentLength(resource.contentLength())
                    .body(resource);

            when(fileTemplateService.generateStaggeredPaymentTemplate())
                    .thenReturn(expectedResponse);

            // When & Then
            mockMvc.perform(get("/api/staggered/template-submission"))
                    .andExpect(status().isOk())
                    .andExpect(content().contentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
                    .andExpect(header().string(HttpHeaders.CONTENT_DISPOSITION,
                        containsString("large_template.xlsx")));

            verify(fileTemplateService, times(1)).generateStaggeredPaymentTemplate();
        }

        @Test
        @DisplayName("Should handle template with custom headers")
        void testGetTemplateFile_CustomHeaders() throws Exception {
            // Given
            byte[] templateContent = createMockExcelContent("Custom Template");
            ByteArrayResource resource = new ByteArrayResource(templateContent);

            ResponseEntity<Resource> expectedResponse = ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"custom template.xlsx\"")
                    .header("X-Custom-Header", "template-value")
                    .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
                    .body(resource);

            when(fileTemplateService.generateStaggeredPaymentTemplate())
                    .thenReturn(expectedResponse);

            // When & Then
            mockMvc.perform(get("/api/staggered/template-submission"))
                    .andExpect(status().isOk())
                    .andExpect(header().string("X-Custom-Header", "template-value"))
                    .andExpect(header().string(HttpHeaders.CONTENT_DISPOSITION,
                        containsString("custom template.xlsx")));

            verify(fileTemplateService, times(1)).generateStaggeredPaymentTemplate();
        }

        @Test
        @DisplayName("Should verify service is called only once")
        void testGetTemplateFile_ServiceCallVerification() throws Exception {
            // Given
            byte[] templateContent = createMockExcelContent("Staggered Payment Template");
            ByteArrayResource resource = new ByteArrayResource(templateContent);

            ResponseEntity<Resource> expectedResponse = ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=staggered_payment_template.xlsx")
                    .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
                    .body(resource);

            when(fileTemplateService.generateStaggeredPaymentTemplate())
                    .thenReturn(expectedResponse);

            // When
            mockMvc.perform(get("/api/staggered/template-submission"))
                    .andExpect(status().isOk());

            // Then - Verify service is called exactly once
            verify(fileTemplateService, times(1)).generateStaggeredPaymentTemplate();
            verifyNoMoreInteractions(fileTemplateService);
        }
    }

    @Nested
    @DisplayName("Template Payment Endpoint Tests")
    class TemplatePaymentTests {

        @Test
        @DisplayName("Should successfully generate and download payment template")
        void testGetPaymentTemplate_Success() throws Exception {
            // Given
            byte[] templateContent = createMockExcelContent("Payment Template");
            ByteArrayResource resource = new ByteArrayResource(templateContent);

            ResponseEntity<Resource> expectedResponse = ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=payment_template.xlsx")
                    .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
                    .contentLength(resource.contentLength())
                    .body(resource);

            when(fileTemplateService.generatePaymentTemplate())
                    .thenReturn(expectedResponse);

            // When & Then
            mockMvc.perform(get("/api/staggered/template-payment"))
                    .andExpect(status().isOk())
                    .andExpect(content().contentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
                    .andExpect(header().exists(HttpHeaders.CONTENT_DISPOSITION))
                    .andExpect(header().string(HttpHeaders.CONTENT_DISPOSITION,
                        containsString("attachment")))
                    .andExpect(header().string(HttpHeaders.CONTENT_DISPOSITION,
                        containsString("payment_template.xlsx")));

            verify(fileTemplateService, times(1)).generatePaymentTemplate();
        }

        @Test
        @DisplayName("Should handle service exception during payment template generation")
        void testGetPaymentTemplate_ServiceException() throws Exception {
            // Given
            when(fileTemplateService.generatePaymentTemplate())
                    .thenThrow(new RuntimeException("Payment template generation failed"));

            // When & Then
            mockMvc.perform(get("/api/staggered/template-payment"))
                    .andExpect(status().isInternalServerError());

            verify(fileTemplateService, times(1)).generatePaymentTemplate();
        }

        @Test
        @DisplayName("Should handle payment template service returning null")
        void testGetPaymentTemplate_NullResponse() throws Exception {
            // Given
            when(fileTemplateService.generatePaymentTemplate())
                    .thenReturn(null);

            // When & Then
            mockMvc.perform(get("/api/staggered/template-payment"))
                    .andExpect(status().isInternalServerError());

            verify(fileTemplateService, times(1)).generatePaymentTemplate();
        }

        @Test
        @DisplayName("Should handle payment template with different formats")
        void testGetPaymentTemplate_DifferentFormat() throws Exception {
            // Given
            byte[] templateContent = createMockExcelContent("CSV Payment Template");
            ByteArrayResource resource = new ByteArrayResource(templateContent);

            ResponseEntity<Resource> expectedResponse = ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=payment_template.csv")
                    .contentType(MediaType.parseMediaType("text/csv"))
                    .body(resource);

            when(fileTemplateService.generatePaymentTemplate())
                    .thenReturn(expectedResponse);

            // When & Then
            mockMvc.perform(get("/api/staggered/template-payment"))
                    .andExpect(status().isOk())
                    .andExpect(content().contentType("text/csv"))
                    .andExpect(header().string(HttpHeaders.CONTENT_DISPOSITION,
                        containsString("payment_template.csv")));

            verify(fileTemplateService, times(1)).generatePaymentTemplate();
        }

        @Test
        @DisplayName("Should handle payment template with special characters in filename")
        void testGetPaymentTemplate_SpecialCharacters() throws Exception {
            // Given
            byte[] templateContent = createMockExcelContent("Special Template");
            ByteArrayResource resource = new ByteArrayResource(templateContent);

            ResponseEntity<Resource> expectedResponse = ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"payment template (2024).xlsx\"")
                    .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
                    .body(resource);

            when(fileTemplateService.generatePaymentTemplate())
                    .thenReturn(expectedResponse);

            // When & Then
            mockMvc.perform(get("/api/staggered/template-payment"))
                    .andExpect(status().isOk())
                    .andExpect(header().string(HttpHeaders.CONTENT_DISPOSITION,
                        containsString("payment template (2024).xlsx")));

            verify(fileTemplateService, times(1)).generatePaymentTemplate();
        }

        @Test
        @DisplayName("Should verify payment template service is called only once")
        void testGetPaymentTemplate_ServiceCallVerification() throws Exception {
            // Given
            byte[] templateContent = createMockExcelContent("Payment Template");
            ByteArrayResource resource = new ByteArrayResource(templateContent);

            ResponseEntity<Resource> expectedResponse = ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=payment_template.xlsx")
                    .contentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
                    .body(resource);

            when(fileTemplateService.generatePaymentTemplate())
                    .thenReturn(expectedResponse);

            // When
            mockMvc.perform(get("/api/staggered/template-payment"))
                    .andExpect(status().isOk());

            // Then - Verify service is called exactly once
            verify(fileTemplateService, times(1)).generatePaymentTemplate();
            verifyNoMoreInteractions(fileTemplateService);
        }
    }

    @Nested
    @DisplayName("Submission History Endpoint Tests")
    class SubmissionHistoryTests {

        @Test
        @DisplayName("Should successfully retrieve submission history with default parameters")
        void testGetSubmissionHistory_Success_DefaultParams() throws Exception {
            // Given
            List<SubmissionHistoryDTO> mockSubmissions = Arrays.asList(
                    SubmissionHistoryDTO.builder()
                            .id(1L)
                            .taskName("TASK-001")
                            .referenceNumber("REF-001")
                            .createdBy("user1")
                            .submitterName("John Doe")
                            .status("PENDING")
                            .nip("123456789")
                            .name("John Doe")
                            .grade("A")
                            .paymentType("Salary")
                            .amount(new BigDecimal("5000000"))
                            .description("Monthly salary")
                            .monthOfProcess("January")
                            .yearOfProcess("2024")
                            .directorate("IT")
                            .eligible(true)
                            .currentReviewer("reviewer1")
                            .paymentDate("-")
                            .remarks("-")
                            .build(),
                    SubmissionHistoryDTO.builder()
                            .id(2L)
                            .taskName("TASK-002")
                            .referenceNumber("REF-002")
                            .createdBy("user2")
                            .submitterName("Jane Smith")
                            .status("APPROVED")
                            .nip("987654321")
                            .name("Jane Smith")
                            .grade("B")
                            .paymentType("Bonus")
                            .amount(new BigDecimal("2000000"))
                            .description("Performance bonus")
                            .monthOfProcess("February")
                            .yearOfProcess("2024")
                            .directorate("HR")
                            .eligible(true)
                            .currentReviewer("")
                            .paymentDate("-")
                            .remarks("-")
                            .build()
            );

            Page<SubmissionHistoryDTO> mockPage = new PageImpl<>(mockSubmissions,
                    PageRequest.of(0, 10, Sort.by(Sort.Direction.DESC, "id")), 2);

            when(submissionHistoryService.getSubmissionHistoryWithApprovalDetails(
                    any(Pageable.class), isNull(), isNull(), isNull(), eq(false), isNull(), isNull(), isNull()))
                    .thenReturn(mockPage);

            // When & Then
            mockMvc.perform(get("/api/staggered/submission/history"))
                    .andExpect(status().isOk())
                    .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                    .andExpect(jsonPath("$.code").value(200))
                    .andExpect(jsonPath("$.status").value("OK"))
                    .andExpect(jsonPath("$.message").value("Submissions retrieved successfully"))
                    .andExpect(jsonPath("$.data.content").isArray())
                    .andExpect(jsonPath("$.data.content.length()").value(2))
                    .andExpect(jsonPath("$.data.content[0].id").value(1))
                    .andExpect(jsonPath("$.data.content[0].taskName").value("TASK-001"))
                    .andExpect(jsonPath("$.data.content[0].status").value("PENDING"))
                    .andExpect(jsonPath("$.data.content[1].id").value(2))
                    .andExpect(jsonPath("$.data.content[1].status").value("APPROVED"))
                    .andExpect(jsonPath("$.data.totalElements").value(2))
                    .andExpect(jsonPath("$.data.size").value(10))
                    .andExpect(jsonPath("$.data.number").value(0));

            verify(submissionHistoryService, times(1))
                    .getSubmissionHistoryWithApprovalDetails(any(Pageable.class), isNull(), isNull(), isNull(), eq(false), isNull(), isNull(), isNull());
        }

        @Test
        @DisplayName("Should successfully retrieve submission history with filtering parameters")
        void testGetSubmissionHistory_Success_WithFilters() throws Exception {
            // Given
            List<SubmissionHistoryDTO> mockSubmissions = Arrays.asList(
                    SubmissionHistoryDTO.builder()
                            .id(1L)
                            .taskName("TASK-001")
                            .status("PENDING")
                            .nip("123456789")
                            .name("John Doe")
                            .eligible(true)
                            .build()
            );

            Page<SubmissionHistoryDTO> mockPage = new PageImpl<>(mockSubmissions,
                    PageRequest.of(0, 5, Sort.by(Sort.Direction.DESC, "id")), 1);

            when(submissionHistoryService.getSubmissionHistoryWithApprovalDetails(
                    any(Pageable.class), eq("PENDING"), eq("2024-01-01"), eq("2024-12-31"), eq(true), eq("123456789"), eq("John"), isNull()))
                    .thenReturn(mockPage);

            // When & Then
            mockMvc.perform(get("/api/staggered/submission/history")
                            .param("page", "0")
                            .param("size", "5")
                            .param("sort", "id,desc")
                            .param("status", "PENDING")
                            .param("startDate", "2024-01-01")
                            .param("endDate", "2024-12-31")
                            .param("currentUserOnly", "true")
                            .param("nip", "123456789")
                            .param("name", "John"))
                    .andExpect(status().isOk())
                    .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                    .andExpect(jsonPath("$.code").value(200))
                    .andExpect(jsonPath("$.status").value("OK"))
                    .andExpect(jsonPath("$.data.content").isArray())
                    .andExpect(jsonPath("$.data.content.length()").value(1))
                    .andExpect(jsonPath("$.data.content[0].status").value("PENDING"))
                    .andExpect(jsonPath("$.data.content[0].nip").value("123456789"));

            verify(submissionHistoryService, times(1))
                    .getSubmissionHistoryWithApprovalDetails(any(Pageable.class), eq("PENDING"), eq("2024-01-01"), eq("2024-12-31"), eq(true), eq("123456789"), eq("John"), isNull());
        }

        @Test
        @DisplayName("Should handle empty submission history results")
        void testGetSubmissionHistory_EmptyResults() throws Exception {
            // Given
            Page<SubmissionHistoryDTO> emptyPage = new PageImpl<>(Arrays.asList(),
                    PageRequest.of(0, 10, Sort.by(Sort.Direction.DESC, "id")), 0);

            when(submissionHistoryService.getSubmissionHistoryWithApprovalDetails(
                    any(Pageable.class), isNull(), isNull(), isNull(), eq(false), isNull(), isNull(), isNull()))
                    .thenReturn(emptyPage);

            // When & Then
            mockMvc.perform(get("/api/staggered/submission/history"))
                    .andExpect(status().isOk())
                    .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                    .andExpect(jsonPath("$.code").value(200))
                    .andExpect(jsonPath("$.status").value("OK"))
                    .andExpect(jsonPath("$.message").value("Submissions retrieved successfully"))
                    .andExpect(jsonPath("$.data.content").isArray())
                    .andExpect(jsonPath("$.data.content.length()").value(0))
                    .andExpect(jsonPath("$.data.totalElements").value(0));

            verify(submissionHistoryService, times(1))
                    .getSubmissionHistoryWithApprovalDetails(any(Pageable.class), isNull(), isNull(), isNull(), eq(false), isNull(), isNull(), isNull());
        }

        @Test
        @DisplayName("Should handle service exception during submission history retrieval")
        void testGetSubmissionHistory_ServiceException() throws Exception {
            // Given
            when(submissionHistoryService.getSubmissionHistoryWithApprovalDetails(
                    any(Pageable.class), isNull(), isNull(), isNull(), eq(false), isNull(), isNull(), isNull()))
                    .thenThrow(new CustomBadRequestException(500, "Database Error", "Database connection failed"));

            // When & Then
            mockMvc.perform(get("/api/staggered/submission/history"))
                    .andExpect(status().isInternalServerError());

            verify(submissionHistoryService, times(1))
                    .getSubmissionHistoryWithApprovalDetails(any(Pageable.class), isNull(), isNull(), isNull(), eq(false), isNull(), isNull(), isNull());
        }

        @Test
        @DisplayName("Should handle invalid date format in parameters")
        void testGetSubmissionHistory_InvalidDateFormat() throws Exception {
            // Given
            when(submissionHistoryService.getSubmissionHistoryWithApprovalDetails(
                    any(Pageable.class), isNull(), eq("invalid-date"), isNull(), eq(false), isNull(), isNull(), isNull()))
                    .thenThrow(new CustomBadRequestException(400, "Invalid Date", "Invalid date format"));

            // When & Then
            mockMvc.perform(get("/api/staggered/submission/history")
                            .param("startDate", "invalid-date"))
                    .andExpect(status().isBadRequest());

            verify(submissionHistoryService, times(1))
                    .getSubmissionHistoryWithApprovalDetails(any(Pageable.class), isNull(), eq("invalid-date"), isNull(), eq(false), isNull(), isNull(), isNull());
        }

        @Test
        @DisplayName("Should handle large page size requests")
        void testGetSubmissionHistory_LargePageSize() throws Exception {
            // Given
            List<SubmissionHistoryDTO> mockSubmissions = Arrays.asList(
                    SubmissionHistoryDTO.builder()
                            .id(1L)
                            .taskName("TASK-001")
                            .status("PENDING")
                            .build()
            );

            Page<SubmissionHistoryDTO> mockPage = new PageImpl<>(mockSubmissions,
                    PageRequest.of(0, 100, Sort.by(Sort.Direction.DESC, "id")), 1);

            when(submissionHistoryService.getSubmissionHistoryWithApprovalDetails(
                    any(Pageable.class), isNull(), isNull(), isNull(), eq(false), isNull(), isNull(), isNull()))
                    .thenReturn(mockPage);

            // When & Then
            mockMvc.perform(get("/api/staggered/submission/history")
                            .param("size", "100"))
                    .andExpect(status().isOk())
                    .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                    .andExpect(jsonPath("$.code").value(200))
                    .andExpect(jsonPath("$.data.size").value(100));

            verify(submissionHistoryService, times(1))
                    .getSubmissionHistoryWithApprovalDetails(any(Pageable.class), isNull(), isNull(), isNull(), eq(false), isNull(), isNull(), isNull());
        }

        @Test
        @DisplayName("Should verify service method is called exactly once")
        void testGetSubmissionHistory_ServiceCallVerification() throws Exception {
            // Given
            Page<SubmissionHistoryDTO> emptyPage = new PageImpl<>(Arrays.asList(),
                    PageRequest.of(0, 10, Sort.by(Sort.Direction.DESC, "id")), 0);

            when(submissionHistoryService.getSubmissionHistoryWithApprovalDetails(
                    any(Pageable.class), isNull(), isNull(), isNull(), eq(false), isNull(), isNull(), isNull()))
                    .thenReturn(emptyPage);

            // When
            mockMvc.perform(get("/api/staggered/submission/history"))
                    .andExpect(status().isOk());

            // Then - Verify service is called exactly once
            verify(submissionHistoryService, times(1))
                    .getSubmissionHistoryWithApprovalDetails(any(Pageable.class), isNull(), isNull(), isNull(), eq(false), isNull(), isNull(), isNull());
            verifyNoMoreInteractions(submissionHistoryService);
        }
    }

    @Nested
    @DisplayName("Submission Detail Endpoint Tests")
    class SubmissionDetailTests {

        @Test
        @DisplayName("Should successfully retrieve submission detail by ID")
        void testGetSubmissionDetail_Success() throws Exception {
            // Given
            Long submissionId = 1L;
            SubmissionDetailDTO mockSubmissionDetail = SubmissionDetailDTO.builder()
                    .id(submissionId)
                    .taskName("TASK-001")
                    .createdAt(LocalDateTime.of(2024, 1, 15, 10, 30))
                    .referenceNumber("REF-001")
                    .createdBy("123456789")
                    .submitterName("John Doe")
                    .submitterJob("Developer")
                    .status("PENDING")
                    .nip("123456789")
                    .name("John Doe")
                    .grade("A")
                    .paymentType("Salary")
                    .amount(new BigDecimal("5000000"))
                    .description("Monthly salary")
                    .monthOfProcess("January")
                    .yearOfProcess("2024")
                    .directorate("IT")
                    .slik("-")
                    .sanction("-")
                    .terminationDate("-")
                    .eligible(true)
                    .currentReviewer("<EMAIL>")
                    .paymentDate("-")
                    .remarks("-")
                    .build();

            when(submissionDetailService.getSubmissionDetail(submissionId))
                    .thenReturn(mockSubmissionDetail);

            // When & Then
            mockMvc.perform(get("/api/staggered/submission/history/{id}", submissionId))
                    .andExpect(status().isOk())
                    .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                    .andExpect(jsonPath("$.code").value(200))
                    .andExpect(jsonPath("$.status").value("OK"))
                    .andExpect(jsonPath("$.message").value("Submission detail retrieved successfully"))
                    .andExpect(jsonPath("$.data.id").value(submissionId))
                    .andExpect(jsonPath("$.data.taskName").value("TASK-001"))
                    .andExpect(jsonPath("$.data.referenceNumber").value("REF-001"))
                    .andExpect(jsonPath("$.data.createdBy").value("123456789"))
                    .andExpect(jsonPath("$.data.submitterName").value("John Doe"))
                    .andExpect(jsonPath("$.data.status").value("PENDING"))
                    .andExpect(jsonPath("$.data.nip").value("123456789"))
                    .andExpect(jsonPath("$.data.name").value("John Doe"))
                    .andExpect(jsonPath("$.data.grade").value("A"))
                    .andExpect(jsonPath("$.data.paymentType").value("Salary"))
                    .andExpect(jsonPath("$.data.amount").value(5000000))
                    .andExpect(jsonPath("$.data.description").value("Monthly salary"))
                    .andExpect(jsonPath("$.data.monthOfProcess").value("January"))
                    .andExpect(jsonPath("$.data.yearOfProcess").value("2024"))
                    .andExpect(jsonPath("$.data.directorate").value("IT"))
                    .andExpect(jsonPath("$.data.eligible").value(true))
                    .andExpect(jsonPath("$.data.currentReviewer").value("<EMAIL>"));

            verify(submissionDetailService, times(1)).getSubmissionDetail(submissionId);
        }

        @Test
        @DisplayName("Should handle submission not found")
        void testGetSubmissionDetail_NotFound() throws Exception {
            // Given
            Long submissionId = 999L;
            when(submissionDetailService.getSubmissionDetail(submissionId))
                    .thenThrow(CustomBadRequestException.badRequest("Submission not found with ID: " + submissionId));

            // When & Then
            mockMvc.perform(get("/api/staggered/submission/history/{id}", submissionId))
                    .andExpect(status().isBadRequest());

            verify(submissionDetailService, times(1)).getSubmissionDetail(submissionId);
        }

        @Test
        @DisplayName("Should handle access denied")
        void testGetSubmissionDetail_AccessDenied() throws Exception {
            // Given
            Long submissionId = 1L;
            when(submissionDetailService.getSubmissionDetail(submissionId))
                    .thenThrow(CustomBadRequestException.badRequest("Access denied. You can only view your own submissions."));

            // When & Then
            mockMvc.perform(get("/api/staggered/submission/history/{id}", submissionId))
                    .andExpect(status().isBadRequest());

            verify(submissionDetailService, times(1)).getSubmissionDetail(submissionId);
        }

        @Test
        @DisplayName("Should handle invalid submission ID format")
        void testGetSubmissionDetail_InvalidIdFormat() throws Exception {
            // When & Then
            mockMvc.perform(get("/api/staggered/submission/history/{id}", "invalid"))
                    .andExpect(status().isBadRequest());

            verifyNoInteractions(submissionDetailService);
        }

        @Test
        @DisplayName("Should verify service method is called exactly once")
        void testGetSubmissionDetail_ServiceCallVerification() throws Exception {
            // Given
            Long submissionId = 1L;
            SubmissionDetailDTO mockSubmissionDetail = SubmissionDetailDTO.builder()
                    .id(submissionId)
                    .taskName("TASK-001")
                    .createdAt(LocalDateTime.of(2024, 1, 15, 10, 30))
                    .referenceNumber("REF-001")
                    .createdBy("123456789")
                    .submitterName("John Doe")
                    .status("PENDING")
                    .build();

            when(submissionDetailService.getSubmissionDetail(submissionId))
                    .thenReturn(mockSubmissionDetail);

            // When
            mockMvc.perform(get("/api/staggered/submission/history/{id}", submissionId))
                    .andExpect(status().isOk());

            // Then - Verify service is called exactly once
            verify(submissionDetailService, times(1)).getSubmissionDetail(submissionId);
            verifyNoMoreInteractions(submissionDetailService);
        }
    }

    @Nested
    @DisplayName("Transaction Console Log Endpoint Tests")
    class TransactionConsoleLogTests {

        @Test
        @DisplayName("Should successfully retrieve transaction console log by task name")
        void testGetTransactionConsoleLog_Success() throws Exception {
            // Given
            String taskName = "TASK-001";
            ApiResponse mockApiResponse = new ApiResponse();
            mockApiResponse.setSuccess(true);
            mockApiResponse.setMessage("Success");
            mockApiResponse.setData("Transaction log data");

            when(submissionDetailService.getTransactionConsoleLog(taskName))
                    .thenReturn(mockApiResponse);

            // When & Then
            mockMvc.perform(get("/api/staggered/log-{taskname}", taskName))
                    .andExpect(status().isOk())
                    .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                    .andExpect(jsonPath("$.code").value(200))
                    .andExpect(jsonPath("$.status").value("OK"))
                    .andExpect(jsonPath("$.message").value("Transaction console log retrieved successfully"))
                    .andExpect(jsonPath("$.data").value("Transaction log data"));

            verify(submissionDetailService, times(1)).getTransactionConsoleLog(taskName);
        }

        @Test
        @DisplayName("Should handle service exception")
        void testGetTransactionConsoleLog_ServiceException() throws Exception {
            // Given
            String taskName = "TASK-001";
            when(submissionDetailService.getTransactionConsoleLog(taskName))
                    .thenThrow(CustomBadRequestException.badRequest("Failed to retrieve transaction console log"));

            // When & Then
            mockMvc.perform(get("/api/staggered/log-{taskname}", taskName))
                    .andExpect(status().isBadRequest());

            verify(submissionDetailService, times(1)).getTransactionConsoleLog(taskName);
        }

        @Test
        @DisplayName("Should handle empty task name")
        void testGetTransactionConsoleLog_EmptyTaskName() throws Exception {
            // Given
            String taskName = "";

            // When & Then
            mockMvc.perform(get("/api/staggered/log-{taskname}", taskName))
                    .andExpect(status().isNotFound()); // Spring will return 404 for empty path variable

            verifyNoInteractions(submissionDetailService);
        }

        @Test
        @DisplayName("Should verify service method is called exactly once")
        void testGetTransactionConsoleLog_ServiceCallVerification() throws Exception {
            // Given
            String taskName = "TASK-001";
            ApiResponse mockApiResponse = new ApiResponse();
            mockApiResponse.setSuccess(true);
            mockApiResponse.setMessage("Success");
            mockApiResponse.setData("Log data");

            when(submissionDetailService.getTransactionConsoleLog(taskName))
                    .thenReturn(mockApiResponse);

            // When
            mockMvc.perform(get("/api/staggered/log-{taskname}", taskName))
                    .andExpect(status().isOk());

            // Then - Verify service is called exactly once
            verify(submissionDetailService, times(1)).getTransactionConsoleLog(taskName);
            verifyNoMoreInteractions(submissionDetailService);
        }
    }

    @Nested
    @DisplayName("Approval Task List Endpoint Tests")
    class ApprovalTaskListTests {

        @Test
        @DisplayName("Should successfully retrieve approval task list with default parameters")
        void testGetApprovalTaskList_Success_DefaultParams() throws Exception {
            // Given
            List<TaskListDTO> mockTasks = Arrays.asList(
                    TaskListDTO.builder()
                            .id(1L)
                            .taskName("TASK-001")
                            .referenceNumber("REF-001")
                            .submitterName("John Doe")
                            .submitterJob("Developer")
                            .status("PENDING")
                            .nip("123456789")
                            .name("John Doe")
                            .grade("A")
                            .paymentType("Salary")
                            .amount(new BigDecimal("5000000"))
                            .description("Monthly salary")
                            .monthOfProcess("January")
                            .yearOfProcess("2024")
                            .directorate("IT")
                            .eligible(true)
                            .build(),
                    TaskListDTO.builder()
                            .id(2L)
                            .taskName("TASK-002")
                            .referenceNumber("REF-002")
                            .submitterName("Jane Smith")
                            .submitterJob("Analyst")
                            .status("PENDING")
                            .nip("987654321")
                            .name("Jane Smith")
                            .grade("B")
                            .paymentType("Bonus")
                            .amount(new BigDecimal("2000000"))
                            .description("Performance bonus")
                            .monthOfProcess("February")
                            .yearOfProcess("2024")
                            .directorate("HR")
                            .eligible(true)
                            .build()
            );

            Page<TaskListDTO> mockPage = new PageImpl<>(mockTasks,
                    PageRequest.of(0, 10, Sort.by(Sort.Direction.DESC, "id")), 2);

            when(taskListService.getApprovalTaskList(
                    any(Pageable.class), isNull(), isNull(), isNull(), isNull(), isNull()))
                    .thenReturn(mockPage);

            // When & Then
            mockMvc.perform(get("/api/staggered/list-approval"))
                    .andExpect(status().isOk())
                    .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                    .andExpect(jsonPath("$.code").value(200))
                    .andExpect(jsonPath("$.status").value("OK"))
                    .andExpect(jsonPath("$.message").value("Approval tasks retrieved successfully"))
                    .andExpect(jsonPath("$.data.content").isArray())
                    .andExpect(jsonPath("$.data.content.length()").value(2))
                    .andExpect(jsonPath("$.data.content[0].id").value(1))
                    .andExpect(jsonPath("$.data.content[0].taskName").value("TASK-001"))
                    .andExpect(jsonPath("$.data.content[0].status").value("PENDING"))
                    .andExpect(jsonPath("$.data.content[0].submitterName").value("John Doe"))
                    .andExpect(jsonPath("$.data.content[1].id").value(2))
                    .andExpect(jsonPath("$.data.content[1].taskName").value("TASK-002"))
                    .andExpect(jsonPath("$.data.content[1].status").value("PENDING"))
                    .andExpect(jsonPath("$.data.totalElements").value(2))
                    .andExpect(jsonPath("$.data.size").value(10))
                    .andExpect(jsonPath("$.data.number").value(0));

            verify(taskListService, times(1))
                    .getApprovalTaskList(any(Pageable.class), isNull(), isNull(), isNull(), isNull(), isNull());
        }

        @Test
        @DisplayName("Should successfully retrieve approval task list with filtering parameters")
        void testGetApprovalTaskList_Success_WithFilters() throws Exception {
            // Given
            List<TaskListDTO> mockTasks = Arrays.asList(
                    TaskListDTO.builder()
                            .id(1L)
                            .taskName("TASK-001")
                            .status("PENDING")
                            .nip("123456789")
                            .name("John Doe")
                            .eligible(true)
                            .build()
            );

            Page<TaskListDTO> mockPage = new PageImpl<>(mockTasks,
                    PageRequest.of(0, 5, Sort.by(Sort.Direction.DESC, "id")), 1);

            when(taskListService.getApprovalTaskList(
                    any(Pageable.class), eq("PENDING"), eq("2024-01-01"), eq("2024-12-31"), eq("123456789"), eq("John")))
                    .thenReturn(mockPage);

            // When & Then
            mockMvc.perform(get("/api/staggered/list-approval")
                            .param("page", "0")
                            .param("size", "5")
                            .param("sort", "id,desc")
                            .param("status", "PENDING")
                            .param("startDate", "2024-01-01")
                            .param("endDate", "2024-12-31")
                            .param("nip", "123456789")
                            .param("name", "John"))
                    .andExpect(status().isOk())
                    .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                    .andExpect(jsonPath("$.code").value(200))
                    .andExpect(jsonPath("$.status").value("OK"))
                    .andExpect(jsonPath("$.data.content").isArray())
                    .andExpect(jsonPath("$.data.content.length()").value(1))
                    .andExpect(jsonPath("$.data.content[0].status").value("PENDING"))
                    .andExpect(jsonPath("$.data.content[0].nip").value("123456789"));

            verify(taskListService, times(1))
                    .getApprovalTaskList(any(Pageable.class), eq("PENDING"), eq("2024-01-01"), eq("2024-12-31"), eq("123456789"), eq("John"));
        }

        @Test
        @DisplayName("Should handle empty approval task list results")
        void testGetApprovalTaskList_EmptyResults() throws Exception {
            // Given
            Page<TaskListDTO> emptyPage = new PageImpl<>(Arrays.asList(),
                    PageRequest.of(0, 10, Sort.by(Sort.Direction.DESC, "id")), 0);

            when(taskListService.getApprovalTaskList(
                    any(Pageable.class), isNull(), isNull(), isNull(), isNull(), isNull()))
                    .thenReturn(emptyPage);

            // When & Then
            mockMvc.perform(get("/api/staggered/list-approval"))
                    .andExpect(status().isOk())
                    .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                    .andExpect(jsonPath("$.code").value(200))
                    .andExpect(jsonPath("$.status").value("OK"))
                    .andExpect(jsonPath("$.message").value("Approval tasks retrieved successfully"))
                    .andExpect(jsonPath("$.data.content").isArray())
                    .andExpect(jsonPath("$.data.content.length()").value(0))
                    .andExpect(jsonPath("$.data.totalElements").value(0));

            verify(taskListService, times(1))
                    .getApprovalTaskList(any(Pageable.class), isNull(), isNull(), isNull(), isNull(), isNull());
        }

        @Test
        @DisplayName("Should handle service exception during approval task list retrieval")
        void testGetApprovalTaskList_ServiceException() throws Exception {
            // Given
            when(taskListService.getApprovalTaskList(
                    any(Pageable.class), isNull(), isNull(), isNull(), isNull(), isNull()))
                    .thenThrow(new RuntimeException("Current user not found"));

            // When & Then
            mockMvc.perform(get("/api/staggered/list-approval"))
                    .andExpect(status().isInternalServerError());

            verify(taskListService, times(1))
                    .getApprovalTaskList(any(Pageable.class), isNull(), isNull(), isNull(), isNull(), isNull());
        }

        @Test
        @DisplayName("Should handle invalid date format in parameters")
        void testGetApprovalTaskList_InvalidDateFormat() throws Exception {
            // Given
            when(taskListService.getApprovalTaskList(
                    any(Pageable.class), isNull(), eq("invalid-date"), isNull(), isNull(), isNull()))
                    .thenThrow(new RuntimeException("Invalid date format"));

            // When & Then
            mockMvc.perform(get("/api/staggered/list-approval")
                            .param("startDate", "invalid-date"))
                    .andExpect(status().isInternalServerError());

            verify(taskListService, times(1))
                    .getApprovalTaskList(any(Pageable.class), isNull(), eq("invalid-date"), isNull(), isNull(), isNull());
        }

        @Test
        @DisplayName("Should handle large page size requests")
        void testGetApprovalTaskList_LargePageSize() throws Exception {
            // Given
            List<TaskListDTO> mockTasks = Arrays.asList(
                    TaskListDTO.builder()
                            .id(1L)
                            .taskName("TASK-001")
                            .status("PENDING")
                            .build()
            );

            Page<TaskListDTO> mockPage = new PageImpl<>(mockTasks,
                    PageRequest.of(0, 100, Sort.by(Sort.Direction.DESC, "id")), 1);

            when(taskListService.getApprovalTaskList(
                    any(Pageable.class), isNull(), isNull(), isNull(), isNull(), isNull()))
                    .thenReturn(mockPage);

            // When & Then
            mockMvc.perform(get("/api/staggered/list-approval")
                            .param("size", "100"))
                    .andExpect(status().isOk())
                    .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                    .andExpect(jsonPath("$.code").value(200))
                    .andExpect(jsonPath("$.data.size").value(100));

            verify(taskListService, times(1))
                    .getApprovalTaskList(any(Pageable.class), isNull(), isNull(), isNull(), isNull(), isNull());
        }

        @Test
        @DisplayName("Should verify service method is called exactly once")
        void testGetApprovalTaskList_ServiceCallVerification() throws Exception {
            // Given
            Page<TaskListDTO> emptyPage = new PageImpl<>(Arrays.asList(),
                    PageRequest.of(0, 10, Sort.by(Sort.Direction.DESC, "id")), 0);

            when(taskListService.getApprovalTaskList(
                    any(Pageable.class), isNull(), isNull(), isNull(), isNull(), isNull()))
                    .thenReturn(emptyPage);

            // When
            mockMvc.perform(get("/api/staggered/list-approval"))
                    .andExpect(status().isOk());

            // Then - Verify service is called exactly once
            verify(taskListService, times(1))
                    .getApprovalTaskList(any(Pageable.class), isNull(), isNull(), isNull(), isNull(), isNull());
            verifyNoMoreInteractions(taskListService);
        }
    }

    @Nested
    @DisplayName("Report Endpoint Tests")
    class ReportTests {

        @Test
        @DisplayName("Should successfully generate Excel report")
        void testGenerateReport_Success() throws Exception {
            // Given
            String requestJson = """
                {
                    "startDate": "2024-01-01",
                    "endDate": "2024-01-31",
                    "status": "Pending"
                }
                """;

            byte[] excelContent = createMockExcelContent("Report");
            ByteArrayResource resource = new ByteArrayResource(excelContent);
            ResponseEntity<Resource> mockResponse = ResponseEntity.ok()
                    .header("Content-Disposition", "attachment; filename=submission_report_20240101_120000.xlsx")
                    .body(resource);

            when(reportService.generateReportExcel(any())).thenReturn(mockResponse);

            // When & Then
            mockMvc.perform(post("/api/staggered/report")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(requestJson))
                    .andExpect(status().isOk())
                    .andExpect(header().string("Content-Disposition",
                            "attachment; filename=submission_report_20240101_120000.xlsx"));

            verify(reportService).generateReportExcel(any());
        }

        @Test
        @DisplayName("Should handle validation error in report generation")
        void testGenerateReport_ValidationError() throws Exception {
            // Given
            String requestJson = """
                {
                    "startDate": "invalid-date",
                    "endDate": "2024-01-31",
                    "status": "Pending"
                }
                """;

            when(reportService.generateReportExcel(any()))
                    .thenThrow(new CustomBadRequestException(400, "Bad Request", "Invalid date format"));

            // When & Then
            mockMvc.perform(post("/api/staggered/report")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(requestJson))
                    .andExpect(status().isBadRequest())
                    .andExpect(jsonPath("$.code").value(400))
                    .andExpect(jsonPath("$.status").value("Bad Request"))
                    .andExpect(jsonPath("$.message").value("Invalid date format"));

            verify(reportService).generateReportExcel(any());
        }

        @Test
        @DisplayName("Should handle no data found error")
        void testGenerateReport_NoDataFound() throws Exception {
            // Given
            String requestJson = """
                {
                    "startDate": "2024-01-01",
                    "endDate": "2024-01-31",
                    "status": "NonExistentStatus"
                }
                """;

            when(reportService.generateReportExcel(any()))
                    .thenThrow(new CustomBadRequestException(404, "No Data Found",
                            "No submissions found matching the specified criteria"));

            // When & Then
            mockMvc.perform(post("/api/staggered/report")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(requestJson))
                    .andExpect(status().isNotFound())
                    .andExpect(jsonPath("$.code").value(404))
                    .andExpect(jsonPath("$.status").value("No Data Found"))
                    .andExpect(jsonPath("$.message").value("No submissions found matching the specified criteria"));

            verify(reportService).generateReportExcel(any());
        }

        @Test
        @DisplayName("Should handle internal server error")
        void testGenerateReport_InternalServerError() throws Exception {
            // Given
            String requestJson = """
                {
                    "startDate": "2024-01-01",
                    "endDate": "2024-01-31",
                    "status": "Pending"
                }
                """;

            when(reportService.generateReportExcel(any()))
                    .thenThrow(new RuntimeException("Unexpected error"));

            // When & Then
            mockMvc.perform(post("/api/staggered/report")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(requestJson))
                    .andExpect(status().isInternalServerError())
                    .andExpect(jsonPath("$.code").value(500))
                    .andExpect(jsonPath("$.status").value("Internal Server Error"))
                    .andExpect(jsonPath("$.message").value("Failed to generate report Excel: Unexpected error"));

            verify(reportService).generateReportExcel(any());
        }

        @Test
        @DisplayName("Should handle request with null filters")
        void testGenerateReport_NullFilters() throws Exception {
            // Given
            String requestJson = """
                {
                    "startDate": null,
                    "endDate": null,
                    "status": null
                }
                """;

            byte[] excelContent = createMockExcelContent("Report");
            ByteArrayResource resource = new ByteArrayResource(excelContent);
            ResponseEntity<Resource> mockResponse = ResponseEntity.ok()
                    .header("Content-Disposition", "attachment; filename=submission_report_20240101_120000.xlsx")
                    .body(resource);

            when(reportService.generateReportExcel(any())).thenReturn(mockResponse);

            // When & Then
            mockMvc.perform(post("/api/staggered/report")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(requestJson))
                    .andExpect(status().isOk())
                    .andExpect(header().string("Content-Disposition",
                            "attachment; filename=submission_report_20240101_120000.xlsx"));

            verify(reportService).generateReportExcel(any());
        }
    }

    /**
     * Helper method to create mock Excel content
     */
    private byte[] createMockExcelContent(String content) {
        return (content + " - Mock Excel Content").getBytes();
    }
}
